<template>
  <div class="my-tasks">
    <div class="title">我的任务中心</div>
    <div class="c-title">管理您的任务申请和邀请，跟踪执行进度</div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <div class="filter-controls">
        <label>任务分类</label>
        <el-select v-model="filters.source" style="width: 150px">
          <el-option label="全部任务" value="" />
          <el-option label="📝 我申请的" value="application" />
          <el-option label="🔔 邀请我的" value="invitation" />
        </el-select>

        <label>类型</label>
        <el-select v-model="filters.taskType" style="width: 150px">
          <el-option label="全部类型" value="" />
          <el-option label="📝 推文" value="post" />
          <el-option label="📹 视频" value="video" />
          <el-option label="📰 文章" value="article" />
          <el-option label="📺 直播" value="live_stream" />
          <el-option label="🎤 AMA活动" value="ama_activity" />
        </el-select>

        <label>全部状态</label>
        <el-select v-model="filters.status" style="width: 150px">
          <el-option label="全部状态" value="" />
          <el-option label="📝 已申请" value="application-pending" />
          <el-option label="📨 被邀请" value="invitation-pending" />
          <el-option label="🚀 已分配" value="assigned" />
          <el-option label="🟠 待审核" value="unapproved" />
          <el-option label="✅ 审核通过" value="approved" />
          <el-option label="🔄 运行中" value="running" />
          <el-option label="🎉 已完成" value="completed" />
          <el-option label="❌ 审核失败" value="rejected" />
          <el-option label="🚫 申请被拒绝" value="application-rejected" />
          <el-option label="🙅 已拒绝邀请" value="invitation-rejected" />
          <el-option label="⏳ 已失效" value="expired" />
        </el-select>

        <label>搜索任务</label>
        <el-input v-model="filters.keyword" style="width: 200px" clearable />

        <el-button @click="fetchMyTasks" :loading="loading" text>
          🔄 刷新
        </el-button>
      </div>
    </div>

    <!-- 任务列表区域 -->
    <div class="tasks-section">
      <div v-if="loading" class="loading">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="filteredTasks.length === 0" class="no-tasks">
        <el-empty :description="getEmptyDescription()" />
      </div>

      <div v-else class="task-list">
        <div v-for="task in filteredTasks" :key="`${task.source}-${task.invitation_id}`" class="task-card">
          <div class="task-title">
            <span class="task-icon">{{ getTaskTypeIcon(task.task_type) }}</span>
            <span class="task-name">{{ task.task_name }}</span>
          </div>

          <ul class="info-list">
            <li>
              <label>📅 创建时间</label>
              <span>{{ formatDate(task.create_time) }}</span>
            </li>
            <li>
              <label>{{ getTaskTypeIcon(task.task_type) }} 任务类型</label>
              <span>{{ getTaskTypeLabel(task.task_type) }}</span>
            </li>
            <li>
              <label>🏆 奖励类型</label>
              <span class="reward-type-display">
                <el-tag size="small" :type="getRewardTypeColor(getInferredRewardType(task))" effect="plain" class="reward-type-tag">
                  {{ getRewardTypeDisplayName(getInferredRewardType(task)) }}
                </el-tag>
              </span>
            </li>
            <!-- 基础奖励 - 品牌推广和品牌+转化模式显示 -->
            <li v-if="getInferredRewardType(task) !== 'commission'">
              <label>💰 基础奖励</label>
              <span class="reward-amount base">
                <span class="currency">$</span>
                <span class="amount">{{ task.base_reward || 0 }}</span>
              </span>
            </li>
            <!-- 按转化付费FTT - 根据奖励类型显示不同内容 -->
            <li v-if="getInferredRewardType(task) === 'branding_plus_conversion'">
              <label>🎯 按转化付费FTT</label>
              <span class="reward-amount performance">
                <span class="currency">$</span>
                <span class="amount">{{ task.performance_rate || 0 }}</span>
                <span class="unit">/{{ getPerformanceUnit(task.task_type) }}</span>
              </span>
            </li>
            <!-- 佣金比例 - 带单返佣模式显示 -->
            <li v-if="getInferredRewardType(task) === 'commission'">
              <label>💸 佣金比例</label>
              <span class="reward-amount commission">
                <span class="amount">{{ task.commission_rate || 0 }}</span>
                <span class="unit">%</span>
              </span>
            </li>
            <li>
              <label>📅 执行时间</label>
              <span>{{ formatDate(task.start_date) }} ~ {{ formatDate(task.end_date) }}</span>
            </li>
            <li>
              <label>{{ getSourceIcon(task.source) }} 来源</label>
              <span>{{ getSourceLabel(task.source) }}</span>
            </li>
            <!-- 渠道码展示 -->
            <li v-if="task.channel_code">
              <label>🔗 渠道码</label>
              <span class="channel-code">{{ task.channel_code }}</span>
            </li>
            <li>
              <label>{{ getStatusIcon(task) }} 状态</label>
              <span :class="getStatusClass(task)">{{ getDisplayStatus(task)
                }}</span>
            </li>
            <li v-if="shouldShowDraftContent(task)">
              <label>📝 草稿提交</label>
              <span>{{ formatDateTime(task.draft_submit_time) }}</span>
            </li>
          </ul>

          <!-- 任务卡片头部 -->

          <!-- 任务卡片内容 -->
          <div class="task-content">

            <!-- 任务描述/留言/反馈 -->
            <div class="task-description">
              <div v-if="task.source === 'invitation' && task.message" class="description-content">
                <span>📄 邀请留言</span>: {{ task.message }}
              </div>
              <div v-else-if="task.source === 'application' && task.application_reason" class="description-content">
                <span>📄 申请理由</span>: {{ task.application_reason }}
              </div>
              <div v-else-if="task.task_status === 'assigned' && task.description" class="description-content">
                <span>📄 任务描述</span>: {{ truncateText(task.description, 100) }}
              </div>
              <div v-else-if="task.task_status === 'unapproved' && task.review_feedback" class="description-content">
                <span>📄 审核反馈</span>: {{ task.review_feedback }}
              </div>
              <div v-else-if="task.task_status === 'completed'" class="description-content">
                <span>📄 任务结果</span>: 任务已成功完成，感谢您的参与
              </div>

              <!-- 草稿内容展示 -->
              <el-collapse accordion>
                <el-collapse-item v-if="shouldShowDraftContent(task)" title="📝 草稿内容" name="1">
                  <div class="draft-content-section">
                    <!-- 草稿内容 -->
                    <div class="draft-text-box">
                      {{ getDraftContentPreview(task.draft_content, 200) }}
                    </div>

                    <!-- 创作说明 -->
                    <div v-if="getDraftCreationNotes(task.draft_content)" class="creation-notes-box">
                      <div class="notes-title">💡 创作说明:</div>
                      <div class="notes-content">{{ getDraftCreationNotes(task.draft_content) }}</div>
                    </div>

                    <!-- 草稿素材展示 -->
                    <div v-if="getDraftMaterials(task.draft_content).length > 0" class="draft-materials-box">
                      <div class="materials-title">📎 创作素材:</div>
                      <div class="draft-materials-preview">
                        <div v-for="(material, index) in getDraftMaterials(task.draft_content)" :key="index" class="draft-material-item">
                          <!-- 图片预览 -->
                          <div v-if="material.type === 'image'" class="material-image">
                            <el-image
                              :src="material.url"
                              :preview-src-list="[material.url]"
                              fit="cover"
                            >
                              <template #error>
                                <div class="image-error">
                                  <el-icon><Picture /></el-icon>
                                </div>
                              </template>
                            </el-image>
                          </div>

                          <!-- 视频预览 -->
                          <div v-else-if="material.type === 'video'" class="material-video">
                            <video
                              :src="material.url"
                              controls
                              preload="metadata"
                              class="video-player"
                            ></video>
                          </div>

                          <!-- 链接预览 -->
                          <div v-else class="material-link">
                            <el-link :href="material.url || material.name" target="_blank" type="primary">
                              {{ material.name || '素材链接' }}
                            </el-link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
                <!-- 已发布链接展示 -->
                <el-collapse-item v-if="shouldShowPublishedLinks(task)" title="🔗 已发布链接" name="2">
                  <div class="published-links-section">
                    <div class="published-links-box">
                      <div v-for="(link, index) in getPublishedLinks(task.published_links)" :key="index" class="published-link-item">
                        <span class="link-icon">{{ getPlatformIcon(getPublishedPlatform(task.published_links)) }}</span>
                        <a :href="link" target="_blank" class="link-url">{{ link }}</a>
                      </div>
                      <div class="publish-time">
                        📅 发布时间：{{ getPublishedTime(task.published_links) }}
                      </div>
                    </div>
                  </div>
                </el-collapse-item>

                <!-- 官方素材展示 -->
                <el-collapse-item  v-if="task.official_materials && parseMaterials(task.official_materials).length > 0" title="🎨 官方素材" name="3">
                  <div class="official-materials-section">
                    <div class="materials-preview">
                      <div v-for="(material, index) in parseMaterials(task.official_materials)" :key="index" class="material-item">
                        <!-- 图片预览 -->
                        <div v-if="material.type === 'image'" class="material-image">
                          <el-image
                            :src="material.url"
                            :preview-src-list="[material.url]"
                            fit="cover"
                          >
                            <template #error>
                              <div class="image-error">
                                <el-icon><Picture /></el-icon>
                              </div>
                            </template>
                          </el-image>
                        </div>

                        <!-- 视频预览 -->
                        <div v-else-if="material.type === 'video'" class="material-video">
                          <video
                            :src="material.url"
                            controls
                            preload="metadata"
                            class="video-player"
                          ></video>
                        </div>

                        <!-- 链接预览 -->
                        <div v-else class="material-link">
                          <el-link :href="material.url || material" target="_blank" type="primary">
                            <el-icon><Link /></el-icon>
                            素材链接
                          </el-link>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>

            <!-- 操作按钮 -->
            <div class="task-actions">

              <!-- 根据状态显示不同操作按钮 -->
              <template v-if="getTaskActionType(task) === 'invitation-pending'">
                <el-button type="success" @click="acceptInvitation(task)">
                  接受
                </el-button>
                <el-button type="danger" @click="rejectInvitation(task)">
                  拒绝
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'application-pending'">
                <el-button type="warning" @click="withdrawApplication(task)">
                  撤回申请
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'in-progress'">
                <el-button type="primary" @click="submitContent(task)">
                  📝 提交草稿
                </el-button>
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'under-review'">
                <el-button type="warning" @click="showFeedback(task)">
                  👁️ 查看反馈
                </el-button>
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'approved'">
                <el-button type="success" @click="submitPublishLinks(task)">
                  🔗 提交发布链接
                </el-button>
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'task-rejected'">
                <el-button type="warning" @click="showFeedback(task)">
                  👁️ 查看反馈
                </el-button>
                <el-button type="primary" @click="resubmitContent(task)">
                  📝 重新提交草稿
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'running'">
                <el-button type="primary" @click="submitPublishLinks(task)">
                  🔗 添加发布链接
                </el-button>
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'completed'">
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
                <el-button type="success" @click="viewEarnings(task)">
                  💰 查看收益
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'expired'">
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>

              <template v-else-if="getTaskActionType(task) === 'invitation-rejected'">
                <el-button v-if="hasSubmittedTwitterLinks(task)" @click="viewTaskData(task)">
                  📊 查看数据
                </el-button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container" v-if="totalTasks > pageSize">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalTasks"
        layout="total, prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 任务操作弹窗 -->
    <TaskOperationDialog v-model="operationDialogVisible" :task="selectedTask" :operation="currentOperation"
      @success="handleOperationSuccess" />

    <!-- 新增弹窗组件 -->
    <TaskDataDialog v-model="taskDataDialogVisible" :task="selectedTask" />

    <TaskEarningsDialog v-model="earningsDialogVisible" :task="selectedTask" />

    <DraftSubmitDialog v-model="draftSubmitDialogVisible" :task="selectedTask" @success="handleOperationSuccess" />

    <SubmitLinksDialog v-model="publishLinksDialogVisible" :task="selectedTask" @success="handleOperationSuccess" />


  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import apiService from '@/utils/api'

// 组件导入
import TaskOperationDialog from '@/components/TaskOperationDialog.vue'
import TaskDataDialog from '@/components/TaskDataDialog.vue'
import TaskEarningsDialog from '@/components/TaskEarningsDialog.vue'
import DraftSubmitDialog from '@/components/DraftSubmitDialog.vue'
import SubmitLinksDialog from '@/components/SubmitLinksDialog.vue'

// 数据状态
const tasks = ref([])
const loading = ref(false)

// 筛选器状态
const filters = ref({
  source: '',      // 任务来源: '' | 'application' | 'invitation'
  taskType: '',    // 任务类型: '' | 'post' | 'video' | 'article' | 'live_stream' | 'ama_activity'
  status: '',      // 任务状态: '' | 'pending' | 'assigned' | 'unapproved' | 'approved' | 'completed' | 'rejected' | 'expired'
  keyword: ''      // 搜索关键词
})

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)

// 弹窗状态
const operationDialogVisible = ref(false)
const selectedTask = ref(null)
const currentOperation = ref('')

// 新增弹窗状态
const taskDataDialogVisible = ref(false)
const earningsDialogVisible = ref(false)
const draftSubmitDialogVisible = ref(false)
const publishLinksDialogVisible = ref(false)



// 筛选后的任务列表
const filteredTasks = computed(() => {
  let filtered = tasks.value

  // 按来源筛选
  if (filters.value.source) {
    filtered = filtered.filter(task => task.source === filters.value.source)
  }

  // 按任务类型筛选
  if (filters.value.taskType) {
    filtered = filtered.filter(task => task.task_type === filters.value.taskType)
  }

  // 按状态筛选 - 使用原始状态判断
  if (filters.value.status) {
    filtered = filtered.filter(task => {
      // 兼容新旧数据结构
      const invitationStatus = task.invitation_status || task.status

      switch (filters.value.status) {
        case 'application-pending':
          return task.source === 'application' && invitationStatus === 'pending'
        case 'invitation-pending':
          return task.source === 'invitation' && invitationStatus === 'pending'
        case 'assigned':
          return invitationStatus === 'accepted' && task.task_status === 'assigned'
        case 'unapproved':
          return invitationStatus === 'accepted' && task.task_status === 'unapproved'
        case 'approved':
          return invitationStatus === 'accepted' && task.task_status === 'approved'
        case 'running':
          return invitationStatus === 'accepted' && task.task_status === 'running'
        case 'completed':
          return invitationStatus === 'accepted' && task.task_status === 'completed'
        case 'rejected':
          return invitationStatus === 'accepted' && task.task_status === 'rejected'
        case 'application-rejected':
          return task.source === 'application' && invitationStatus === 'rejected'
        case 'invitation-rejected':
          return task.source === 'invitation' && invitationStatus === 'rejected'
        case 'expired':
          return invitationStatus === 'expired'
        default:
          return true
      }
    })
  }

  // 按关键词搜索
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    filtered = filtered.filter(task =>
      task.task_name?.toLowerCase().includes(keyword) ||
      task.description?.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 数据获取方法
async function fetchMyTasks() {
  loading.value = true
  try {
    console.log('🚀 开始获取任务数据...')

    // 优先使用新的统一接口
    try {
      const response = await apiService.getKolMyTasks({
        page: currentPage.value,
        size: pageSize.value
      })
      // const response = {
      //   data: [
      //     {
      //       source: 'application',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       invitation_status: 'accepted',
      //       task_status: 'completed',
      //       application_reason: '一堆乱七八糟的留言，巴拉巴拉'
      //     },
      //     {
      //       source: 'application',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       invitation_status: 'accepted',
      //       task_status: 'approved',
      //       application_reason: '一堆乱七八糟的留言，巴拉巴拉'
      //     },
      //     {
      //       source: 'application',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       invitation_status: 'accepted',
      //       task_status: 'assigned',
      //       application_reason: '一堆乱七八糟的留言，巴拉巴拉'
      //     },
      //     {
      //       source: 'application',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼,任务叫什么呀，吼吼吼,任务叫什么呀，吼吼吼,任务叫什么呀，吼吼吼,任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       invitation_status: 'accepted',
      //       task_status: 'unapproved',
      //       application_reason: '申请理由巴拉巴拉申请理由巴拉巴拉申请理由巴拉巴拉申请理由巴拉巴拉申请理由巴拉巴拉申请理由巴拉巴拉申请理由巴拉巴拉'
      //     },
      //     {
      //       source: 'invitation',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       task_status: 'pending',
      //       status: 'pending',
      //       message: '一堆乱七八糟的留言，巴拉巴拉'
      //     },
      //     {
      //       source: 'application',
      //       invitation_id: 123,
      //       task_id: 321,
      //       create_time: +new Date(),
      //       task_name: '这ee个e 任务叫什么呀，吼吼吼',
      //       task_type: 'post',
      //       base_reward: 122,
      //       performance_rate: 0.5,
      //       start_date: +new Date(),
      //       end_date: +new Date(),
      //       task_status: 'pending',
      //       status: 'pending',
      //       application_reason: '一堆乱七八糟的留言，巴拉巴拉'
      //     }
      //   ]
      // };

      // 处理分页响应
      if (response.data && typeof response.data === 'object' && response.data.data) {
        // 新的分页格式
        tasks.value = Array.isArray(response.data.data) ? response.data.data : []
        totalTasks.value = response.data.total || 0
        console.log('📋 统一接口获取分页任务数据:', {
          tasks: tasks.value.length,
          total: totalTasks.value,
          page: currentPage.value,
          size: pageSize.value
        })
      } else {
        // 兼容旧格式（非分页）
        tasks.value = Array.isArray(response.data) ? response.data : []
        totalTasks.value = tasks.value.length
        console.log('📋 统一接口获取任务数据（兼容模式）:', tasks.value)
      }
    } catch (error) {
      // 如果新接口不可用，回退到原有方式
      console.warn('统一接口不可用，使用原有方式:', error)

      const [applicationsResponse, invitationsResponse] = await Promise.all([
        apiService.getKolMyApplications(), // 我申请的任务
        apiService.getKolInvitations() // 收到的邀请
      ])

      console.log('📝 申请数据:', applicationsResponse.data)
      console.log('🔔 邀请数据:', invitationsResponse.data)

      const applications = Array.isArray(applicationsResponse.data) ? applicationsResponse.data : []
      const invitations = Array.isArray(invitationsResponse.data) ? invitationsResponse.data : []

      // 合并申请和邀请数据（后端已经添加了source标识）
      tasks.value = [...applications, ...invitations]
      console.log('📋 分别获取任务数据:', tasks.value)
    }

  } catch (error) {
    console.error('获取任务失败:', error)
    ElMessage.error('获取任务失败')
  } finally {
    loading.value = false
  }
}

// 辅助方法 - 任务类型
function getTaskTypeIcon(type) {
  const icons = {
    'post': '📝',
    'video': '📹',
    'article': '📰',
    'live_stream': '📺',
    'ama_activity': '🎤'
  }
  return icons[type] || '📝'
}

function getTaskTypeLabel(type) {
  const labels = {
    'post': '推文 (post)',
    'video': '视频 (video)',
    'article': '文章 (article)',
    'live_stream': '直播 (live_stream)',
    'ama_activity': 'AMA活动 (ama_activity)'
  }
  return labels[type] || type
}

function getPerformanceUnit(type) {
  const units = {
    'post': '点击',
    'video': '观看',
    'article': '阅读',
    'live_stream': '观看',
    'ama_activity': '参与者'
  }
  return units[type] || '次'
}

// 辅助方法 - 来源
function getSourceIcon(source) {
  return source === 'application' ? '📝' : '🔔'
}

function getSourceLabel(source) {
  return source === 'application' ? '我申请的' : '邀请我的'
}

// 辅助方法 - 状态
function getStatusIcon(task) {
  // 兼容新旧数据结构
  const invitationStatus = task.invitation_status || task.status

  if (invitationStatus === 'pending') {
    return task.source === 'application' ? '📝' : '📨'  // 已申请:文档 / 被邀请:邮件
  }
  if (invitationStatus === 'rejected') {
    return task.source === 'application' ? '🚫' : '🙅'  // 申请被拒绝:禁止 / 已拒绝邀请:拒绝手势
  }
  if (invitationStatus === 'expired') return '⏳'  // 已失效:沙漏

  if (invitationStatus === 'accepted') {
    switch (task.task_status) {
      case 'assigned': return '🚀'   // 已分配:火箭
      case 'unapproved': return '🔍' // 待审核:放大镜
      case 'approved': return '✅'   // 审核通过:对勾
      case 'rejected': return '❌'   // 审核失败:叉号
      case 'completed': return '🎉'  // 已完成:庆祝
      default: return '⚪'
    }
  }

  return '⚪'
}

function getDisplayStatus(task) {
  // 兼容新旧数据结构
  const invitationStatus = task.invitation_status || task.status

  // 邀请层面的状态
  if (invitationStatus === 'pending') {
    return task.source === 'application' ? '已申请' : '被邀请'
  }
  if (invitationStatus === 'rejected') {
    return task.source === 'application' ? '申请被拒绝' : '已拒绝邀请'
  }
  if (invitationStatus === 'expired') return '已失效'

  // 已接受的邀请，显示任务执行状态
  if (invitationStatus === 'accepted') {
    switch (task.task_status) {
      case 'assigned': return '已分配'
      case 'unapproved': return '待审核'
      case 'approved': return '审核通过'
      case 'running': return '运行中'
      case 'rejected': return '审核失败'
      case 'completed': return '已完成'
      default: return '未知状态'
    }
  }

  return '未知状态'
}

function getStatusClass(task) {
  const status = getDisplayStatus(task)
  const classes = {
    '已申请': 'status-pending',
    '被邀请': 'status-pending',
    '已分配': 'status-in-progress',
    '待审核': 'status-under-review',
    '审核通过': 'status-approved',
    '运行中': 'status-running',
    '审核失败': 'status-rejected',
    '已完成': 'status-completed',
    '已失效': 'status-expired',
    '申请被拒绝': 'status-rejected',
    '已拒绝邀请': 'status-rejected'
  }
  return classes[status] || ''
}

// 辅助方法 - 操作类型判断
function getTaskActionType(task) {
  // 兼容新旧数据结构
  const invitationStatus = task.invitation_status || task.status

  // 邀请待处理
  if (invitationStatus === 'pending') {
    return task.source === 'invitation' ? 'invitation-pending' : 'application-pending'
  }

  // 已接受的任务，根据任务状态显示操作
  if (invitationStatus === 'accepted') {
    switch (task.task_status) {
      case 'assigned': return 'in-progress'
      case 'unapproved': return 'under-review'
      case 'approved': return 'approved'
      case 'running': return 'running'
      case 'rejected': return 'task-rejected'  // 任务审核失败
      case 'completed': return 'completed'
      default: return 'unknown'
    }
  }

  // 其他状态
  if (invitationStatus === 'rejected') return 'invitation-rejected'  // 邀请/申请被拒绝
  if (invitationStatus === 'expired') return 'expired'

  return 'unknown'
}

// 辅助方法 - 检查是否已提交Twitter链接
function hasSubmittedTwitterLinks(task) {
  // 首先检查任务状态 - 只有特定状态才可能有提交的链接
  const validStatuses = ['running', 'completed']
  if (!validStatuses.includes(task.task_status)) {
    return false
  }

  // 检查是否有 published_links 数据
  if (!task.published_links) return false

  try {
    // 解析 published_links 数据
    const publishedData = typeof task.published_links === 'string'
      ? JSON.parse(task.published_links)
      : task.published_links

    // 数组格式，检查是否有Twitter平台的链接
    if (Array.isArray(publishedData)) {
      return publishedData.some(submission =>
        submission.platform === 'twitter' &&
        submission.links &&
        submission.links.length > 0
      )
    }

    return false
  } catch (error) {
    console.warn('解析 published_links 失败:', error)
    return false
  }
}

// 辅助方法 - 检查是否显示草稿内容
function shouldShowDraftContent(task) {
  const validStatuses = ['unapproved', 'approved', 'running', 'completed']
  return validStatuses.includes(task.task_status) && task.draft_content
}

// 辅助方法 - 解析草稿内容并返回预览文本
function getDraftContentPreview(draftContent, maxLength = 100) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.content) {
      return truncateText(draftData.content, maxLength)
    }
  } catch (error) {
    // 解析失败，当作纯文本处理
  }

  return truncateText(draftContent, maxLength)
}

// 辅助方法 - 获取创作说明
function getDraftCreationNotes(draftContent) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.creation_notes) {
      return draftData.creation_notes
    }
  } catch (error) {
    // 解析失败，返回空
  }

  return ''
}

// 辅助方法 - 获取草稿素材
function getDraftMaterials(draftContent) {
  if (!draftContent) return []

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.materials) {
      return draftData.materials || []
    }
  } catch (error) {
    // 解析失败，返回空数组
  }

  return []
}

// 辅助方法 - 获取素材图标
function getMaterialIcon(materialType) {
  const iconMap = {
    'image': '🖼️',
    'video': '📹',
    'link': '🔗',
    'file': '📄'
  }
  return iconMap[materialType] || '📎'
}

// 辅助方法 - 检查是否显示已发布链接
function shouldShowPublishedLinks(task) {
  const validStatuses = ['running', 'completed']
  return validStatuses.includes(task.task_status) && task.published_links
}

// 辅助方法 - 获取已发布链接列表（数组格式）
function getPublishedLinks(publishedLinksData) {
  if (!publishedLinksData) return []

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    // 数组格式，支持多次提交
    if (Array.isArray(publishedData)) {
      const allLinks = []
      publishedData.forEach(submission => {
        if (submission.links && Array.isArray(submission.links)) {
          allLinks.push(...submission.links)
        }
      })
      return allLinks
    }

    return []
  } catch (error) {
    console.warn('解析 published_links 失败:', error)
    return []
  }
}

// 辅助方法 - 获取发布平台
function getPublishedPlatform(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    return publishedData.platform || ''
  } catch (error) {
    return ''
  }
}

// 辅助方法 - 获取发布时间
function getPublishedTime(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    const publishTime = publishedData.publish_time || publishedData.submit_time
    return publishTime ? formatDateTime(publishTime) : '未知时间'
  } catch (error) {
    return '未知时间'
  }
}

// 辅助方法 - 获取平台图标
function getPlatformIcon(platform) {
  const iconMap = {
    'twitter': '🐦',
    'youtube': '📺',
    'facebook': '📘',
    'instagram': '📸'
  }
  return iconMap[platform] || '🔗'
}

// 辅助方法 - 推断奖励类型
function getInferredRewardType(task) {
  // 如果有明确的 reward_type 字段，直接使用
  if (task.reward_type) {
    return task.reward_type
  }

  // 根据任务数据推断奖励类型
  if (task.commission_rate && task.commission_rate > 0) {
    return 'commission'  // 带单返佣
  } else if (task.performance_rate && task.performance_rate > 0) {
    return 'branding_plus_conversion'  // 品牌+转化
  } else {
    return 'branding'  // 品牌推广
  }
}

// 辅助方法 - 获取奖励类型显示名称
function getRewardTypeDisplayName(type) {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌+转化'
  }
  return typeMap[type] || '品牌推广'
}

// 辅助方法 - 获取奖励类型颜色
function getRewardTypeColor(type) {
  // 如果没有 reward_type 字段，默认为 info (蓝色)
  if (!type) {
    return 'info'
  }

  const colorMap = {
    'branding': 'info',
    'commission': 'success',
    'branding_plus_conversion': 'warning'
  }
  return colorMap[type] || 'info'  // 默认为 info (蓝色)
}

// 辅助方法 - 解析官方素材
function parseMaterials(materials) {
  if (!materials) return []

  // 如果已经是数组格式，直接返回
  if (Array.isArray(materials)) {
    return materials
  }

  // 如果是字符串，尝试解析为JSON
  if (typeof materials === 'string') {
    try {
      const parsed = JSON.parse(materials)
      if (Array.isArray(parsed)) {
        return parsed
      }
    } catch (error) {
      // JSON解析失败，按逗号分隔处理（向后兼容）
      return materials.split(',')
        .map(link => link.trim())
        .filter(link => link && link.length > 0)
        .map(link => ({ url: link, type: 'link' }))
    }
  }

  return []
}

// 操作方法
function viewTaskDetail(task) {
  console.log('查看任务详情:', task)

  // 显示任务详情弹窗
  ElMessageBox.alert(
    `
    <div style="
      text-align: left;
      color: #fff;
      line-height: 1.6;
      margin: 0;
      padding: 0;
    ">
      <h3 style="color: #409EFF; margin-bottom: 20px; font-size: 18px; margin-top: 0;">${task.task_name}</h3>

      <div style="margin-bottom: 12px;">
        <span style="color: #aaa; font-weight: 500;">任务类型:</span>
        <span style="color: #fff; margin-left: 8px;">${getTaskTypeLabel(task.task_type)}</span>
      </div>

      <div style="margin-bottom: 12px;">
        <span style="color: #aaa; font-weight: 500;">基础奖励:</span>
        <span style="color: #67C23A; margin-left: 8px; font-weight: 600;">$${task.base_reward}</span>
      </div>

      <div style="margin-bottom: 12px;">
        <span style="color: #aaa; font-weight: 500;">效果奖励:</span>
        <span style="color: #E6A23C; margin-left: 8px; font-weight: 600;">$${task.performance_rate || 0}/${getPerformanceUnit(task.task_type)}</span>
      </div>

      <div style="margin-bottom: 12px;">
        <span style="color: #aaa; font-weight: 500;">执行时间:</span>
        <span style="color: #fff; margin-left: 8px;">${formatDate(task.start_date)} ~ ${formatDate(task.end_date)}</span>
      </div>

      <div style="margin-bottom: 12px;">
        <span style="color: #aaa; font-weight: 500;">任务来源:</span>
        <span style="color: #409EFF; margin-left: 8px;">${getSourceLabel(task.source)}</span>
      </div>

      <div style="margin-bottom: 16px;">
        <span style="color: #aaa; font-weight: 500;">当前状态:</span>
        <span style="color: #E6A23C; margin-left: 8px; font-weight: 600;">${getDisplayStatus(task)}</span>
      </div>

      ${task.description ? `
        <div style="margin-bottom: 16px; padding: 12px; background: #1e1f25; border-radius: 6px; border-left: 3px solid #409EFF;">
          <div style="color: #aaa; font-weight: 500; margin-bottom: 8px;">任务描述:</div>
          <div style="color: #ccc;">${task.description}</div>
        </div>
      ` : ''}

      ${task.message ? `
        <div style="margin-bottom: 16px; padding: 12px; background: #1e1f25; border-radius: 6px; border-left: 3px solid #67C23A;">
          <div style="color: #aaa; font-weight: 500; margin-bottom: 8px;">邀请留言:</div>
          <div style="color: #ccc;">${task.message}</div>
        </div>
      ` : ''}

      ${task.application_reason ? `
        <div style="margin-bottom: 16px; padding: 12px; background: #1e1f25; border-radius: 6px; border-left: 3px solid #E6A23C;">
          <div style="color: #aaa; font-weight: 500; margin-bottom: 8px;">申请理由:</div>
          <div style="color: #ccc;">${task.application_reason}</div>
        </div>
      ` : ''}

      ${task.review_feedback ? `
        <div style="margin-bottom: 16px; padding: 12px; background: #1e1f25; border-radius: 6px; border-left: 3px solid #F56C6C;">
          <div style="color: #aaa; font-weight: 500; margin-bottom: 8px;">审核反馈:</div>
          <div style="color: #ccc;">${task.review_feedback}</div>
        </div>
      ` : ''}
    </div>
    `,
    '任务详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
      customClass: 'dark-message-box',
      showClose: true,
      closeOnClickModal: true,
      closeOnPressEscape: true
    }
  )
}

function acceptInvitation(task) {
  console.log('接受邀请:', task)

  // 直接接受邀请，使用任务本身的渠道码
  ElMessageBox.confirm(
    `确定要接受任务"${task.task_name}"的邀请吗？`,
    '确认接受',
    {
      confirmButtonText: '接受',
      cancelButtonText: '取消',
      type: 'success'
    }
  ).then(async () => {
    try {
      console.log('开始接受邀请:', {
        invitation_id: task.invitation_id,
        channel_code: task.channel_code
      })

      // 调用接受邀请API，传递任务的渠道码
      const response = await apiService.respondKolInvitations({
        invitation_id: task.invitation_id,
        action: 'accept',
        channel_code: task.channel_code
      })

      console.log('接受邀请成功:', response)
      ElMessage.success('邀请已接受！')

      // 刷新任务列表
      fetchMyTasks()
    } catch (error) {
      console.error('接受邀请失败:', error)
      ElMessage.error(error.response?.data?.detail || '接受邀请失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

function rejectInvitation(task) {
  console.log('拒绝邀请:', task)
  ElMessageBox.prompt(
    `确定要拒绝任务"${task.task_name}"的邀请吗？`,
    '确认拒绝',
    {
      confirmButtonText: '拒绝',
      cancelButtonText: '取消',
      type: 'warning',
      inputPlaceholder: '请输入拒绝原因（可选）',
      inputType: 'textarea'
    }
  ).then(async ({ value }) => {
    try {
      // 调用拒绝邀请API
      const response = await apiService.respondKolInvitations({
        invitation_id: task.invitation_id,  // 使用明确的邀请记录ID
        action: 'reject',
        reason: value || '未提供拒绝原因'
      })

      ElMessage.success('邀请已拒绝！')
      fetchMyTasks() // 刷新数据
    } catch (error) {
      console.error('拒绝邀请失败:', error)
      ElMessage.error(error.response?.data?.detail || '拒绝邀请失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

function withdrawApplication(task) {
  console.log('撤回申请:', task)
  ElMessageBox.confirm(
    `确定要撤回对任务"${task.task_name}"的申请吗？`,
    '确认撤回',
    {
      confirmButtonText: '撤回',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 调用撤回申请API（使用reject action来撤回申请）
      const response = await apiService.respondKolInvitations({
        invitation_id: task.invitation_id,  // 使用明确的申请记录ID
        action: 'reject',
        reason: 'KOL主动撤回申请'
      })

      ElMessage.success('申请已撤回！')
      fetchMyTasks() // 刷新数据
    } catch (error) {
      console.error('撤回申请失败:', error)
      ElMessage.error(error.response?.data?.detail || '撤回申请失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

function submitContent(task) {
  console.log('提交内容:', task)
  selectedTask.value = createCleanTaskObject(task)
  draftSubmitDialogVisible.value = true
}

function viewTaskData(task) {
  console.log('查看任务数据:', task)
  selectedTask.value = createCleanTaskObject(task)
  taskDataDialogVisible.value = true
}

function viewFeedback(task) {
  console.log('查看反馈:', task)
  if (task.review_feedback) {
    ElMessageBox.alert(
      task.review_feedback,
      '审核反馈',
      {
        confirmButtonText: '知道了'
      }
    )
  } else {
    ElMessage.info('暂无审核反馈')
  }
}

function resubmitContent(task) {
  console.log('重新提交内容:', task)
  selectedTask.value = createCleanTaskObject(task)
  draftSubmitDialogVisible.value = true
}

function viewEarnings(task) {
  console.log('查看收益:', task)
  selectedTask.value = createCleanTaskObject(task)
  earningsDialogVisible.value = true
}

// 新增操作方法
function submitPublishLinks(task) {
  console.log('提交发布链接:', task)
  selectedTask.value = createCleanTaskObject(task)
  publishLinksDialogVisible.value = true
}

function showFeedback(task) {
  console.log('查看反馈:', task)
  if (task.review_feedback) {
    ElMessageBox.alert(
      task.review_feedback,
      '审核反馈',
      {
        confirmButtonText: '知道了',
        type: 'info'
      }
    )
  } else {
    ElMessage.info('暂无审核反馈')
  }
}

// 操作成功处理
function handleOperationSuccess() {
  console.log('操作成功，刷新任务列表')
  fetchMyTasks()
}

// 工具函数
function createCleanTaskObject(task) {
  // 创建一个干净的task对象，避免Vue属性绑定错误
  return {
    id: task.id || task.task_id,  // 确保有 id 字段
    invitation_id: task.invitation_id,
    task_id: task.task_id,
    task_name: task.task_name || '',
    task_type: task.task_type || '',
    task_status: task.task_status || '',
    description: task.description || '',
    channel_code: task.channel_code || '',  // 添加渠道码字段
    base_reward: task.base_reward || 0,
    performance_rate: task.performance_rate || 0,
    draft_content: task.draft_content || '',
    review_feedback: task.review_feedback || '',
    published_links: task.published_links || '',
    start_date: task.start_date || '',
    end_date: task.end_date || '',
    source: task.source || '',
    invitation_status: task.invitation_status || '',
    message: task.message || '',
    application_reason: task.application_reason || ''
  }
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function getEmptyDescription() {
  const statusDescriptions = {
    '': '暂无任务数据',
    'application-pending': '暂无已申请的任务',
    'invitation-pending': '暂无被邀请的任务',
    'assigned': '暂无已分配的任务',
    'unapproved': '暂无待审核的任务',
    'approved': '暂无审核通过的任务',
    'running': '暂无运行中的任务',
    'completed': '暂无已完成的任务',
    'rejected': '暂无审核失败的任务',
    'application-rejected': '暂无申请被拒绝的任务',
    'invitation-rejected': '暂无已拒绝邀请的任务',
    'expired': '暂无已失效的任务'
  }

  if (filters.value.keyword) {
    return `没有找到包含"${filters.value.keyword}"的任务`
  }

  return statusDescriptions[filters.value.status] || '暂无符合条件的任务'
}

// 分页相关函数
function handlePageChange(page) {
  currentPage.value = page
  fetchMyTasks()
}

// 筛选变化时重置到第一页
function resetToFirstPage() {
  currentPage.value = 1
  fetchMyTasks()
}

// 监听筛选器变化
watch(filters, () => {
  resetToFirstPage()
}, { deep: true })

onMounted(() => {
  fetchMyTasks()
})


</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
}

.c-title {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
}

.page-header h1 {
  color: #fff;
  font-size: 24px;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #aaa;
  margin: 0 0 24px 0;
}



.filter-section {
  margin-top: 32px;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  label {
    color: #666;
  }
}

.tasks-section {
  margin-top: 16px;
}

.task-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-row-gap: 20px;
  grid-column-gap: 20px;
}

.task-card {
  padding: 24px;
  transition: all 0.3s ease;
  background: #fff;
  box-shadow: #eee 2px 1px 2px;
  border: #efefef 1px solid;
  /* border-radius: 5px; */
  .task-title {
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid #ddd;
    .task-name {
      font-size: 16px;
    }
  }
  .info-list {
    list-style: none;
    padding: 0;
    li {
      padding: 5px 0;
      display: flex;
      &>label {
        margin-right: 8px;
        font-weight: bold;
        color: #888;
        &:after {
          content: ': ';
          display: inline;
        }
      }
    }
  }
}

.task-icon {
  font-size: 20px;
  margin-right: 12px;
}

.task-description {
  /* background: #f6f9f8; */
  /* border: #efefee 1px solid; */
  /* padding: 12px; */
  .el-collapse {
    margin-top: 24px;
  }
}

.description-content {
  font-size: 14px;
  line-height: 1.5;
  &>span {
    font-weight: bold;
    color: #888;
  }
}

/* 草稿内容相关样式 */
.draft-content-section {
  margin-top: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
  padding: 12px;
}

.draft-header {
  margin-bottom: 10px;
}

.draft-title {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.draft-text-box {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
  margin: 8px 0;
  color: #333;
  line-height: 1.5;
  font-size: 13px;
  white-space: pre-line;
}

.creation-notes-box {
  margin-top: 10px;
}

.notes-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 6px;
}

.notes-content {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  color: #555;
  line-height: 1.4;
  font-size: 12px;
}

.materials-box {
  margin-top: 10px;
}

.materials-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 6px;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.material-item {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 12px;
}

.material-icon {
  margin-right: 6px;
  font-size: 14px;
}

.material-name {
  color: #555;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 已发布链接相关样式 */
.published-links-section {
  margin-top: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f0f9ff;
  padding: 12px;
}

.published-header {
  margin-bottom: 10px;
}

.published-title {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.published-links-box {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
}

.published-link-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.link-icon {
  margin-right: 8px;
  font-size: 16px;
}

.link-url {
  color: #409EFF;
  text-decoration: none;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.link-url:hover {
  text-decoration: underline;
}

.publish-time {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  text-align: right;
}

/* 奖励类型和金额样式 */
.reward-type-display {
  display: flex;
  align-items: center;
}

.reward-type-tag {
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  padding: 2px 8px;
}

.reward-amount {
  display: flex;
  align-items: baseline;
  gap: 2px;
  font-weight: 600;
}

.reward-amount.base .currency,
.reward-amount.base .amount {
  color: #67C23A;
}

.reward-amount.performance .currency,
.reward-amount.performance .amount {
  color: #E6A23C;
}

.reward-amount.commission .amount {
  color: #409EFF;
}

.reward-amount .currency {
  font-size: 12px;
}

.reward-amount .amount {
  font-size: 14px;
  font-weight: bold;
}

.reward-amount .unit {
  font-size: 11px;
  color: #999;
  margin-left: 2px;
}

.task-actions {
  margin-top: 18px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* 状态样式 */
.status-pending { color: #E6A23C; }
.status-in-progress { color: #409EFF; }
.status-under-review { color: #E6A23C; }
.status-approved { color: #67C23A; }
.status-running { color: #409EFF; }
.status-rejected { color: #F56C6C; }
.status-completed { color: #67C23A; }
.status-expired { color: #909399; }


.no-tasks {
  text-align: center;
  padding: 40px;
}

.loading {
  padding: 20px;
}

/* 分页组件样式 - 与页面风格保持一致 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  margin-top: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-container .el-pagination {
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-color: #dcdfe6;
  --el-pagination-hover-color: #409EFF;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-color: #606266;
}

.pagination-container .el-pagination .el-pager li {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.3s ease;
}

.pagination-container .el-pagination .el-pager li:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

.pagination-container .el-pagination .el-pager li.is-active {
  background: #409EFF;
  border-color: #409EFF;
  color: #ffffff;
}

.pagination-container .el-pagination .btn-prev,
.pagination-container .el-pagination .btn-next {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.pagination-container .el-pagination .btn-prev:hover,
.pagination-container .el-pagination .btn-next:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

.pagination-container .el-pagination .el-select .el-input {
  background: #ffffff;
  border-color: #dcdfe6;
}

.pagination-container .el-pagination .el-select .el-input:hover {
  border-color: #409EFF;
}

/* 渠道码样式 */
.channel-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.official-materials-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.materials-header {
  margin-bottom: 12px;
}

.materials-title {
  font-weight: 600;
  color: #495057;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.material-item {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.material-image .el-image {
  width: 100%;
  height: 120px;
}

.material-video .video-player {
  width: 100%;
  max-height: 120px;
}

.material-link {
  padding: 12px;
  text-align: center;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background: #f8f9fa;
  color: #6c757d;
}


</style>
