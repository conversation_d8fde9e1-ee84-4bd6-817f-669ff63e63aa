import tweepy
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from utils.config import config
from utils.logger import logger

class TwitterDataResponse(BaseModel):
    platform_id: str
    platform_username: str
    platform_name: str
    profile_url: str
    profile_image_url: str
    followers_count: int
    following_count: int
    tweet_count: int
    verified: bool
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    last_synced_at: datetime

class TwitterService:
    """Twitter OAuth集成服务"""

    def __init__(self):
        """初始化Twitter API客户端"""
        # 🔧 OAuth 会话存储（生产环境建议使用 Redis）
        self._oauth_sessions = {}

        try:
            # OAuth 1.0a 认证（用于OAuth流程）
            self.auth = tweepy.OAuthHandler(
                config.TWITTER_API_KEY,
                config.TWITTER_API_SECRET,
                callback=config.TWITTER_CALLBACK_URL
            )

            # Twitter API v2 客户端（用于获取用户数据）
            self.client = tweepy.Client(
                bearer_token=config.TWITTER_BEARER_TOKEN,
                consumer_key=config.TWITTER_API_KEY,
                consumer_secret=config.TWITTER_API_SECRET,
                wait_on_rate_limit=True
            )

            logger.info("Twitter OAuth service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Twitter OAuth service: {str(e)}")
            raise

    def _store_oauth_session(self, oauth_token: str, oauth_token_secret: str, user_id: str):
        """存储 OAuth 会话信息"""
        import time
        import json
        import os

        session_data = {
            'oauth_token_secret': oauth_token_secret,
            'user_id': user_id,
            'timestamp': time.time()
        }

        # 存储到内存
        self._oauth_sessions[oauth_token] = session_data

        # 🔧 同时存储到文件作为备份
        try:
            os.makedirs('/tmp/oauth_sessions', exist_ok=True)
            with open(f'/tmp/oauth_sessions/{oauth_token}.json', 'w') as f:
                json.dump(session_data, f)
            logger.info(f"OAuth session stored (memory + file) for token: {oauth_token[:10]}... | Total sessions: {len(self._oauth_sessions)} | Instance ID: {id(self)}")
        except Exception as e:
            logger.warning(f"Failed to store session to file: {e}")
            logger.info(f"OAuth session stored (memory only) for token: {oauth_token[:10]}... | Total sessions: {len(self._oauth_sessions)} | Instance ID: {id(self)}")

    def _get_oauth_session(self, oauth_token: str) -> Optional[Dict[str, Any]]:
        """获取 OAuth 会话信息"""
        import time
        import json
        import os

        logger.info(f"Looking for OAuth session for token: {oauth_token[:10]}... | Total sessions: {len(self._oauth_sessions)} | Instance ID: {id(self)}")
        logger.info(f"Available tokens: {[token[:10] + '...' for token in self._oauth_sessions.keys()]}")

        session = self._oauth_sessions.get(oauth_token)

        # 🔧 如果内存中没有，尝试从文件加载
        if not session:
            try:
                file_path = f'/tmp/oauth_sessions/{oauth_token}.json'
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        session = json.load(f)
                    logger.info(f"OAuth session loaded from file for token: {oauth_token[:10]}...")
                    # 恢复到内存
                    self._oauth_sessions[oauth_token] = session
            except Exception as e:
                logger.warning(f"Failed to load session from file: {e}")

        if session:
            # 检查会话是否过期（1小时）
            if time.time() - session['timestamp'] > 3600:
                # 清理内存和文件
                if oauth_token in self._oauth_sessions:
                    del self._oauth_sessions[oauth_token]
                try:
                    os.remove(f'/tmp/oauth_sessions/{oauth_token}.json')
                except:
                    pass
                logger.warning(f"OAuth session expired for token: {oauth_token[:10]}...")
                return None
            logger.info(f"OAuth session found for token: {oauth_token[:10]}...")
        else:
            logger.error(f"OAuth session not found for token: {oauth_token[:10]}...")
        return session

    def _cleanup_expired_sessions(self):
        """清理过期的 OAuth 会话"""
        import time
        current_time = time.time()
        expired_tokens = [
            token for token, session in self._oauth_sessions.items()
            if current_time - session['timestamp'] > 3600
        ]
        for token in expired_tokens:
            del self._oauth_sessions[token]
        if expired_tokens:
            logger.info(f"Cleaned up {len(expired_tokens)} expired OAuth sessions")

    async def get_user_data_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """通过用户名获取Twitter用户数据"""
        try:
            # 移除@符号（如果存在）
            username = username.lstrip('@')

            # 使用Twitter API v2获取用户信息
            user_fields = [
                'id', 'name', 'username', 'description', 'location',
                'profile_image_url', 'public_metrics', 'verified',
                'verified_type', 'created_at'
            ]

            # 在异步环境中运行同步的Twitter API调用
            user = await asyncio.to_thread(
                self.client.get_user,
                username=username,
                user_fields=user_fields
            )

            if not user.data:
                logger.warning(f"Twitter user not found: {username}")
                return None

            return self._format_user_data(user.data)

        except tweepy.NotFound:
            logger.warning(f"Twitter user not found: {username}")
            return None
        except tweepy.Unauthorized:
            logger.error("Twitter API unauthorized - check API credentials")
            raise Exception("Twitter API认证失败")
        except tweepy.TooManyRequests:
            logger.error("Twitter API rate limit exceeded")
            raise Exception("Twitter API请求频率超限，请稍后重试")
        except Exception as e:
            logger.error(f"Twitter API error for user {username}: {str(e)}")
            raise Exception(f"获取Twitter数据失败: {str(e)}")

    async def get_user_data_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """通过用户ID获取Twitter用户数据"""
        try:
            user_fields = [
                'id', 'name', 'username', 'description', 'location',
                'profile_image_url', 'public_metrics', 'verified',
                'verified_type', 'created_at'
            ]

            user = await asyncio.to_thread(
                self.client.get_user,
                id=user_id,
                user_fields=user_fields
            )

            if not user.data:
                logger.warning(f"Twitter user not found: {user_id}")
                return None

            return self._format_user_data(user.data)

        except Exception as e:
            logger.error(f"Failed to get user data by ID {user_id}: {str(e)}")
            raise Exception(f"获取Twitter数据失败: {str(e)}")

    def _format_user_data(self, user_data) -> Dict[str, Any]:
        """格式化用户数据"""
        metrics = user_data.public_metrics

        return {
            'platform_id': f"twitter_{user_data.id}",
            'platform_username': user_data.username,
            'platform_name': user_data.name,
            'profile_url': f"https://twitter.com/{user_data.username}",
            'profile_image_url': user_data.profile_image_url.replace('_normal', '_400x400') if user_data.profile_image_url else None,
            'followers_count': metrics.get('followers_count', 0),
            'following_count': metrics.get('following_count', 0),
            'tweet_count': metrics.get('tweet_count', 0),
            'verified': user_data.verified or False,
            'verified_type': getattr(user_data, 'verified_type', None),
            'account_created_at': user_data.created_at,
            'last_synced_at': datetime.now()
        }
    
    async def get_oauth_url(self, state: str) -> str:
        """获取Twitter OAuth授权URL"""
        try:
            # 🔧 修复：使用标准回调URL，不添加自定义参数
            callback_url = config.TWITTER_CALLBACK_URL

            # 创建新的OAuth handler实例（避免并发问题）
            auth = tweepy.OAuthHandler(
                config.TWITTER_API_KEY,
                config.TWITTER_API_SECRET,
                callback=callback_url
            )

            # 生成OAuth授权URL
            auth_url = await asyncio.to_thread(auth.get_authorization_url)
            request_token = auth.request_token

            # 🔧 修复：存储OAuth会话信息，不依赖URL参数
            self._store_oauth_session(
                request_token['oauth_token'],
                request_token['oauth_token_secret'],
                state  # user_id
            )

            # 清理过期会话
            self._cleanup_expired_sessions()

            logger.info(f"OAuth URL generated for user: {state}")
            return auth_url  # 🔧 返回标准授权URL，不添加自定义参数

        except Exception as e:
            logger.error(f"Failed to generate OAuth URL: {str(e)}")
            raise Exception("生成授权链接失败")
    
    async def handle_oauth_callback(self, oauth_token: str, oauth_verifier: str, state: str = None) -> Optional[Dict[str, Any]]:
        """处理Twitter OAuth回调"""
        try:
            # 🔧 修复：从存储的会话中获取token信息
            session = self._get_oauth_session(oauth_token)
            if not session:
                logger.error(f"OAuth session not found for token: {oauth_token[:10]}...")
                raise Exception("OAuth会话已过期或无效")

            oauth_token_secret = session['oauth_token_secret']
            user_id = session['user_id']

            # 创建OAuth handler
            auth = tweepy.OAuthHandler(
                config.TWITTER_API_KEY,
                config.TWITTER_API_SECRET
            )

            # 设置request token
            auth.request_token = {
                'oauth_token': oauth_token,
                'oauth_token_secret': oauth_token_secret
            }

            # 获取access token
            access_token, access_token_secret = await asyncio.to_thread(
                auth.get_access_token,
                oauth_verifier
            )

            # 使用access token创建认证用户的API客户端
            authenticated_client = tweepy.Client(
                consumer_key=config.TWITTER_API_KEY,
                consumer_secret=config.TWITTER_API_SECRET,
                access_token=access_token,
                access_token_secret=access_token_secret
            )

            # 获取认证用户的信息
            user_fields = [
                'id', 'name', 'username', 'description', 'location',
                'profile_image_url', 'public_metrics', 'verified',
                'verified_type', 'created_at'
            ]

            user = await asyncio.to_thread(
                authenticated_client.get_me,
                user_fields=user_fields
            )

            if not user.data:
                return None

            # 🔧 清理已使用的OAuth会话
            if oauth_token in self._oauth_sessions:
                del self._oauth_sessions[oauth_token]
                # 同时清理文件
                try:
                    import os
                    os.remove(f'/tmp/oauth_sessions/{oauth_token}.json')
                except:
                    pass
                logger.info(f"OAuth session cleaned up for token: {oauth_token[:10]}...")

            # 格式化用户数据，包含关联的用户ID
            twitter_data = self._format_user_data(user.data)
            twitter_data['associated_user_id'] = user_id  # 添加关联的用户ID

            return twitter_data

        except Exception as e:
            logger.error(f"OAuth callback failed: {str(e)}")
            raise Exception("OAuth授权处理失败")
    
    async def sync_user_data(self, username: str) -> Optional[Dict[str, Any]]:
        """同步用户数据（与verify_username相同）"""
        return await self.verify_username(username)
    
    async def batch_sync_users(self, usernames: list) -> Dict[str, Any]:
        """批量同步用户数据"""
        results = {}

        for username in usernames:
            try:
                data = await self.verify_username(username)
                results[username] = {
                    'success': True,
                    'data': data
                }
            except Exception as e:
                results[username] = {
                    'success': False,
                    'error': str(e)
                }

        return results

    async def get_tweet_metrics(self, tweet_id: str, original_url: str = None) -> Optional[Dict[str, Any]]:
        """获取推文的公共指标数据"""
        try:
            # 使用 Twitter API v2 获取推文指标
            tweet_fields = ['public_metrics', 'created_at', 'author_id']

            tweet = await asyncio.to_thread(
                self.client.get_tweet,
                id=tweet_id,
                tweet_fields=tweet_fields
            )

            if not tweet.data:
                return None

            # 使用原始 URL 或构建通用 URL
            post_url = original_url if original_url else f"https://twitter.com/twitter/status/{tweet_id}"

            return {
                "post_id": tweet_id,
                "post_url": post_url,
                "post_date": tweet.data.created_at.isoformat() if tweet.data.created_at else None,
                "metrics": {
                    "view_count": tweet.data.public_metrics.get('impression_count', 0),
                    "retweet_count": tweet.data.public_metrics.get('retweet_count', 0),
                    "like_count": tweet.data.public_metrics.get('like_count', 0),
                    "quote_count": tweet.data.public_metrics.get('quote_count', 0),
                    "reply_count": tweet.data.public_metrics.get('reply_count', 0),
                    "bookmark_count": tweet.data.public_metrics.get('bookmark_count', 0)
                },
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取推文指标失败 {tweet_id}: {str(e)}")
            return None

    def extract_tweet_id_from_url(self, url: str) -> Optional[str]:
        """从 Twitter URL 中提取推文 ID"""
        import re
        # 🔧 支持 twitter.com 和 x.com 两种域名
        pattern = r'(?:twitter\.com|x\.com)/\w+/status/(\d+)'
        match = re.search(pattern, url)
        return match.group(1) if match else None

# 🔧 单例实例存储
_twitter_service_instance = None

# 根据环境选择服务实现
def get_twitter_service():
    """获取Twitter服务实例（单例模式）"""
    global _twitter_service_instance

    if _twitter_service_instance is None:
        try:
            # 检查是否配置了Twitter API密钥
            if (hasattr(config, 'TWITTER_BEARER_TOKEN') and
                hasattr(config, 'TWITTER_API_KEY') and
                config.TWITTER_BEARER_TOKEN and
                config.TWITTER_API_KEY):
                _twitter_service_instance = TwitterService()
                logger.info("TwitterService singleton instance created")
            else:
                logger.warning("Twitter API credentials not configured, using mock service")
        except Exception as e:
            logger.warning(f"Failed to initialize Twitter service")

    return _twitter_service_instance

def reset_twitter_service():
    """重置Twitter服务实例（用于测试）"""
    global _twitter_service_instance
    _twitter_service_instance = None
    logger.info("TwitterService singleton instance reset")
