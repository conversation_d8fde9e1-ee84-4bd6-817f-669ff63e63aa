from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import Table
from pydantic import BaseModel, field_validator
from fastapi import HTTPException

from models.kol_profile import KolProfile
from models.db import metadata, engine
from repositories.kol_profile_repository import KolProfileRepository
from utils.logger import logger
from utils.exceptions import DatabaseException

# Pydantic Models (DTOs)
class KolProfileCreate(BaseModel):
    user_id: int
    platform: Optional[str] = None
    profile_url: Optional[str] = None
    platform_id: str
    platform_username: str
    platform_name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    verified: bool = False
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    followers_count: int = 0
    following_count: int = 0
    tweet_count: int = 0
    listed_count: int = 0
    like_count: int = 0
    tag_name: str
    # 🆕 报价信息字段
    single_tweet_price: Optional[Decimal] = None
    single_ftt_price: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None

    # 🔧 添加字段验证器，自动转换 int/float 到 Decimal
    @field_validator('single_tweet_price', 'single_ftt_price', 'commission_rate', mode='before')
    @classmethod
    def convert_to_decimal(cls, v):
        if v is None:
            return v
        if isinstance(v, (int, float)):
            return Decimal(str(v))
        if isinstance(v, str):
            try:
                return Decimal(v)
            except:
                return v
        return v

class KolProfileUpdate(BaseModel):
    platform: Optional[str] = None
    profile_url: Optional[str] = None
    platform_id: Optional[str] = None  # 🔧 修复：添加缺失的 platform_id 字段
    platform_username: Optional[str] = None
    platform_name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    verified: Optional[bool] = None
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    followers_count: Optional[int] = None
    following_count: Optional[int] = None
    tweet_count: Optional[int] = None
    listed_count: Optional[int] = None
    like_count: Optional[int] = None
    last_synced_at: Optional[datetime] = None  # 🔧 修复：添加缺失的 last_synced_at 字段
    tag_name: Optional[str] = None
    # 🆕 报价信息字段
    single_tweet_price: Optional[Decimal] = None
    single_ftt_price: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None

class KolProfileUpsert(BaseModel):
    """用于 upsert 操作的数据模型，所有字段都是可选的"""
    user_id: Optional[int] = None  # 在接口中会被自动设置
    platform: Optional[str] = None
    profile_url: Optional[str] = None
    platform_id: Optional[str] = None
    platform_username: Optional[str] = None
    platform_name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    verified: Optional[bool] = None
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    followers_count: Optional[int] = None
    following_count: Optional[int] = None
    tweet_count: Optional[int] = None
    listed_count: Optional[int] = None
    like_count: Optional[int] = None
    last_synced_at: Optional[datetime] = None
    tag_name: Optional[str] = None
    # 🆕 报价信息字段
    single_tweet_price: Optional[Decimal] = None
    single_ftt_price: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None

class KolProfileResponse(BaseModel):
    id: int
    user_id: int
    platform: Optional[str] = None
    profile_url: Optional[str] = None
    platform_id: str
    platform_username: str
    platform_name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    verified: bool
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    followers_count: int
    following_count: int
    tweet_count: int
    listed_count: int
    like_count: int
    tag_name: str
    last_synced_at: Optional[datetime] = None
    create_time: datetime
    update_time: datetime
    # 🆕 报价信息字段
    single_tweet_price: Optional[Decimal] = None
    single_ftt_price: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None

    # 用户信息字段
    username: Optional[str] = None  # 系统用户名
    nickname: Optional[str] = None  # 用户昵称
    email: Optional[str] = None     # 用户邮箱

    model_config = {'from_attributes': True}

class KolProfileDetailResponse(BaseModel):
    """详细的KOL资料响应模型，包含更多统计信息"""
    id: Optional[int] = None  # 🔧 改为可选
    user_id: int  # 保持必需，因为这是关键字段
    platform: Optional[str] = None
    profile_url: Optional[str] = None
    platform_id: Optional[str] = None
    platform_username: Optional[str] = None
    platform_name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    verified: Optional[bool] = False  # 🔧 改为可选，有默认值
    verified_type: Optional[str] = None
    account_created_at: Optional[datetime] = None
    followers_count: Optional[int] = 0  # 🔧 改为可选，有默认值
    following_count: Optional[int] = 0  # 🔧 改为可选，有默认值
    tweet_count: Optional[int] = 0  # 🔧 改为可选，有默认值
    listed_count: Optional[int] = 0  # 🔧 改为可选，有默认值
    like_count: Optional[int] = 0  # 🔧 改为可选，有默认值
    tag_name: Optional[str] = None
    last_synced_at: Optional[datetime] = None
    create_time: Optional[datetime] = None  # 🔧 改为可选
    update_time: Optional[datetime] = None  # 🔧 改为可选
    # 🆕 报价信息字段
    single_tweet_price: Optional[Decimal] = None
    single_ftt_price: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None

    # 用户信息字段
    username: Optional[str] = None
    nickname: Optional[str] = None
    email: Optional[str] = None

    # 扩展信息
    engagement_rate: Optional[float] = None
    avg_likes_per_post: Optional[float] = None
    total_tasks_completed: Optional[int] = None
    cooperation_status: Optional[str] = None
    rating_score: Optional[float] = None
    recent_posts_count: Optional[int] = None
    
    model_config = {'from_attributes': True}

class KolProfileService:
    """KOL Profile业务逻辑层"""

    def __init__(self, db: Session):
        self.db = db
        self.kol_profile_repo = KolProfileRepository(db)

    def create_kol_profile(self, profile_data: KolProfileCreate) -> KolProfileResponse:
        """创建KOL资料"""
        try:
            # 检查用户是否已有KOL资料
            existing_profile = self.kol_profile_repo.get_by_user_id(profile_data.user_id)
            if existing_profile:
                raise HTTPException(status_code=400, detail="用户已存在KOL资料")
            
            # 检查platform_id是否唯一
            existing_platform = self.kol_profile_repo.get_by_platform_id(profile_data.platform_id)
            if existing_platform:
                raise HTTPException(status_code=400, detail="Platform ID已存在")

            profile_dict = profile_data.model_dump()
            profile = self.kol_profile_repo.create(profile_dict)
            logger.info(f"KOL profile created for user: {profile_data.user_id}")
            return KolProfileResponse.model_validate(profile)
        except Exception as e:
            logger.error(f"Failed to create KOL profile: {str(e)}")
            raise DatabaseException("创建KOL资料失败")

    def get_kol_profile_by_id(self, profile_id: int) -> Optional[KolProfileResponse]:
        """根据ID获取KOL资料"""
        profile = self.kol_profile_repo.get_by_id(profile_id)
        if not profile:
            return None
        return KolProfileResponse.model_validate(profile)

    def get_kol_profile_by_user_id(self, user_id: int) -> Optional[KolProfileResponse]:
        """根据用户ID获取KOL资料"""
        profile = self.kol_profile_repo.get_by_user_id(user_id)
        if not profile:
            return None
        return KolProfileResponse.model_validate(profile)

    def upsert_kol_profile(self, profile_data: KolProfileUpsert) -> KolProfileResponse:
        """创建或更新KOL资料（upsert操作）"""
        try:
            # 检查用户是否已有KOL资料
            existing_profile = self.kol_profile_repo.get_by_user_id(profile_data.user_id)

            if existing_profile:
                # 如果存在，则更新
                logger.info(f"Updating existing KOL profile for user: {profile_data.user_id}")

                # 获取前端明确传入的字段（exclude_unset=True 只排除未设置的字段）
                # 这样可以确保前端传入的所有值都会被更新，包括空字符串、0、False等
                profile_dict = profile_data.model_dump(exclude_unset=True, exclude={'user_id'})

                # 如果没有要更新的字段，直接返回现有资料
                if not profile_dict:
                    logger.info(f"No fields to update for user: {profile_data.user_id}")
                    return KolProfileResponse.model_validate(existing_profile)

                # 直接调用 repository 的 update 方法，更新所有前端传入的字段
                updated_profile = self.kol_profile_repo.update(existing_profile.id, profile_dict)
                logger.info(f"KOL profile updated: {existing_profile.id}, fields: {list(profile_dict.keys())}")
                return KolProfileResponse.model_validate(updated_profile)
            else:
                # 如果不存在，则创建
                logger.info(f"Creating new KOL profile for user: {profile_data.user_id}")

                # 创建时需要检查必填字段
                profile_dict = profile_data.model_dump(exclude_unset=True)

                profile = self.kol_profile_repo.create(profile_dict)
                logger.info(f"KOL profile created for user: {profile_data.user_id}")
                return KolProfileResponse.model_validate(profile)

        except HTTPException:
            # 重新抛出 HTTPException
            raise
        except Exception as e:
            logger.error(f"Failed to upsert KOL profile: {str(e)}")
            raise DatabaseException("创建或更新KOL资料失败")

    def get_kol_profile_detail(self, profile_id: int) -> Optional[KolProfileDetailResponse]:
        """获取KOL详细资料，包含统计信息"""
        profile = self.kol_profile_repo.get_by_id(profile_id)
        if not profile:
            return None
        
        # 获取用户信息
        from models.user import UserInfo
        user = self.db.query(UserInfo).filter(UserInfo.id == profile.user_id).first()
        
        # 构建详细响应数据
        profile_dict = {
            # 基本信息
            'id': profile.id,
            'user_id': profile.user_id,
            'platform': profile.platform,
            'profile_url': profile.profile_url,
            'platform_id': profile.platform_id,
            'platform_username': profile.platform_username,
            'platform_name': profile.platform_name,
            'description': profile.description,
            'location': profile.location,
            'profile_image_url': profile.profile_image_url,
            'verified': profile.verified,
            'verified_type': profile.verified_type,
            'account_created_at': profile.account_created_at,
            'followers_count': profile.followers_count,
            'following_count': profile.following_count,
            'tweet_count': profile.tweet_count,
            'listed_count': profile.listed_count,
            'like_count': profile.like_count,
            'tag_name': profile.tag_name,
            'last_synced_at': profile.last_synced_at,
            'create_time': profile.create_time,
            'update_time': profile.update_time,
            # 🆕 报价信息字段
            'single_tweet_price': profile.single_tweet_price,
            'single_ftt_price': profile.single_ftt_price,
            'commission_rate': profile.commission_rate,

            # 用户信息字段
            'username': user.username if user else None,
            'nickname': user.nickname if user else None,
            'email': user.email if user else None,

            # 计算扩展信息
            'engagement_rate': self._calculate_engagement_rate(profile),
            'avg_likes_per_post': self._calculate_avg_likes_per_post(profile),
            'total_tasks_completed': self._get_total_tasks_completed(profile.user_id),
            'cooperation_status': self._get_cooperation_status(profile.user_id),
            'rating_score': self._get_rating_score(profile.user_id),
            'recent_posts_count': self._get_recent_posts_count(profile),
        }
        
        return KolProfileDetailResponse.model_validate(profile_dict)

    def get_kol_profile_detail_by_user_id(self, user_id: int) -> Optional[KolProfileDetailResponse]:
        """根据用户ID获取KOL详细资料，包含统计信息"""
        try:
            profile = self.kol_profile_repo.get_by_user_id(user_id)
            if not profile:
                # 🔧 修复：如果没有KOL资料，返回None而不是构建空响应
                logger.info(f"No KOL profile found for user: {user_id}")
                return None

            # 获取用户信息
            from models.user import UserInfo
            user = self.db.query(UserInfo).filter(UserInfo.id == profile.user_id).first()

            # 🔧 确保所有字段都有值，避免传入空字典
            profile_dict = {
                # 基本信息 - 确保必需字段有值
                'id': profile.id,
                'user_id': profile.user_id,  # 🔧 确保这个必需字段有值
                'platform': profile.platform,
                'profile_url': profile.profile_url,
                'platform_id': profile.platform_id,
                'platform_username': profile.platform_username,
                'platform_name': profile.platform_name,
                'description': profile.description,
                'location': profile.location,
                'profile_image_url': profile.profile_image_url,
                'verified': profile.verified if profile.verified is not None else False,
                'verified_type': profile.verified_type,
                'account_created_at': profile.account_created_at,
                'followers_count': profile.followers_count if profile.followers_count is not None else 0,
                'following_count': profile.following_count if profile.following_count is not None else 0,
                'tweet_count': profile.tweet_count if profile.tweet_count is not None else 0,
                'listed_count': profile.listed_count if profile.listed_count is not None else 0,
                'like_count': profile.like_count if profile.like_count is not None else 0,
                'tag_name': profile.tag_name,
                'last_synced_at': profile.last_synced_at,
                'create_time': profile.create_time,
                'update_time': profile.update_time,
                # 🆕 报价信息字段
                'single_tweet_price': profile.single_tweet_price,
                'single_ftt_price': profile.single_ftt_price,
                'commission_rate': profile.commission_rate,

                # 用户信息字段
                'username': user.username if user else None,
                'nickname': user.nickname if user else None,
                'email': user.email if user else None,

                # 计算扩展信息
                'engagement_rate': self._calculate_engagement_rate(profile),
                'avg_likes_per_post': self._calculate_avg_likes_per_post(profile),
                'total_tasks_completed': self._get_total_tasks_completed(profile.user_id),
                'cooperation_status': self._get_cooperation_status(profile.user_id),
                'rating_score': self._get_rating_score(profile.user_id),
                'recent_posts_count': self._get_recent_posts_count(profile),
            }
            
            logger.info(f"Building KolProfileDetailResponse for user {user_id} with data: {profile_dict}")
            return KolProfileDetailResponse.model_validate(profile_dict)
        
        except Exception as e:
            logger.error(f"Error in get_kol_profile_detail_by_user_id for user {user_id}: {str(e)}")
            return None

    def _calculate_engagement_rate(self, profile: KolProfile) -> float:
        """计算互动率"""
        if profile.followers_count == 0 or profile.tweet_count == 0:
            return 0.0
        # 简化计算：(总点赞数 / (粉丝数 * 帖子数)) * 100
        engagement_rate = (profile.like_count / (profile.followers_count * profile.tweet_count)) * 100
        return round(engagement_rate, 2)

    def _calculate_avg_likes_per_post(self, profile: KolProfile) -> float:
        """计算平均每条帖子的点赞数"""
        if profile.tweet_count == 0:
            return 0.0
        avg_likes = profile.like_count / profile.tweet_count
        return round(avg_likes, 1)

    def _get_total_tasks_completed(self, user_id: int) -> int:
        """获取已完成的任务总数"""
        # TODO: 这里需要根据实际的任务表来查询
        # 暂时返回模拟数据
        return 5

    def _get_cooperation_status(self, user_id: int) -> str:
        """获取合作状态"""
        # TODO: 根据实际业务逻辑判断合作状态
        # 暂时返回模拟状态
        return "可邀请"

    def _get_rating_score(self, user_id: int) -> float:
        """获取评分"""
        # TODO: 根据实际的评分系统计算
        # 暂时返回模拟评分
        return 4.5

    def _get_recent_posts_count(self, profile: KolProfile) -> int:
        """获取最近发布数量"""
        # TODO: 根据实际的帖子数据计算最近30天的发布数量
        # 暂时返回模拟数据
        return 15

    def update_kol_profile(self, profile_id: int, update_data: KolProfileUpdate) -> KolProfileResponse:
        """更新KOL资料"""
        profile = self.kol_profile_repo.get_by_id(profile_id)
        if not profile:
            raise HTTPException(status_code=404, detail="KOL资料不存在")

        # 🔧 修复：准备更新数据（包含显式设置的 None 值）
        update_dict = {}
        # 使用 exclude_unset=True 只获取显式设置的字段，包括 None 值
        for field, value in update_data.model_dump(exclude_unset=True).items():
            update_dict[field] = value  # 允许 None 值，用于清除字段

        if not update_dict:
            return KolProfileResponse.model_validate(profile)

        updated_profile = self.kol_profile_repo.update(profile_id, update_dict)
        logger.info(f"KOL profile updated: {profile_id}, fields: {list(update_dict.keys())}")
        return KolProfileResponse.model_validate(updated_profile)

    def get_kol_profiles(self, skip: int = 0, limit: int = 100,
                        platform: Optional[str] = None,
                        tag_name: Optional[str] = None) -> List[KolProfileResponse]:
        """获取KOL资料列表（支持筛选）"""
        profiles = self.kol_profile_repo.get_profiles(skip, limit, platform, tag_name)
        
        # 获取用户信息并构建响应
        response_profiles = []
        for profile in profiles:
            # 获取用户信息
            from models.user import UserInfo
            user = self.db.query(UserInfo).filter(UserInfo.id == profile.user_id).first()
            
            # 构建响应数据
            profile_dict = {
                'id': profile.id,
                'user_id': profile.user_id,
                'platform': profile.platform,
                'profile_url': profile.profile_url,
                'platform_id': profile.platform_id,
                'platform_username': profile.platform_username,
                'platform_name': profile.platform_name,
                'description': profile.description,
                'location': profile.location,
                'profile_image_url': profile.profile_image_url,
                'verified': profile.verified,
                'verified_type': profile.verified_type,
                'account_created_at': profile.account_created_at,
                'followers_count': profile.followers_count,
                'following_count': profile.following_count,
                'tweet_count': profile.tweet_count,
                'listed_count': profile.listed_count,
                'like_count': profile.like_count,
                'tag_name': profile.tag_name,
                'last_synced_at': profile.last_synced_at,
                'create_time': profile.create_time,
                'update_time': profile.update_time,
                'single_tweet_price': profile.single_tweet_price,
                'single_ftt_price': profile.single_ftt_price,
                'commission_rate': profile.commission_rate,
                # 用户信息字段
                'username': user.username if user else None,
                'nickname': user.nickname if user else None,
                'email': user.email if user else None,
            }
            
            response_profiles.append(KolProfileResponse.model_validate(profile_dict))
        
        return response_profiles

    # Twitter绑定专用方法
    def bind_twitter_account(self, user_id: int, twitter_data: dict) -> KolProfileResponse:
        """绑定Twitter账号"""
        try:
            # 检查用户是否已有KOL资料
            existing_profile = self.get_kol_profile_by_user_id(user_id)

            if existing_profile:
                # 更新现有资料的Twitter信息
                update_data = self._format_twitter_data(twitter_data)
                return self.update_kol_profile(existing_profile.id, KolProfileUpdate(**update_data))
            else:
                # 创建新的KOL资料
                profile_data = self._format_twitter_data(twitter_data)
                profile_data['user_id'] = user_id
                profile_data['tag_name'] = 'DeFi'  # 默认标签
                return self.create_kol_profile(KolProfileCreate(**profile_data))

        except Exception as e:
            logger.error(f"Failed to bind Twitter account for user {user_id}: {str(e)}")
            raise Exception("绑定Twitter账号失败")

    def sync_twitter_data(self, user_id: int, twitter_data: dict) -> KolProfileResponse:
        """同步Twitter数据"""
        try:
            profile = self.get_kol_profile_by_user_id(user_id)
            if not profile:
                raise ValueError("未找到KOL资料")

            # 只更新可变的Twitter数据
            sync_data = {
                'followers_count': twitter_data['followers_count'],
                'following_count': twitter_data['following_count'],
                'tweet_count': twitter_data['tweet_count'],
                'listed_count': twitter_data.get('listed_count', 0),
                'like_count': twitter_data.get('like_count', 0),
                'profile_image_url': twitter_data['profile_image_url'],
                'verified': twitter_data['verified'],
                'verified_type': twitter_data.get('verified_type'),
                'last_synced_at': datetime.now()
            }

            return self.update_kol_profile(profile.id, KolProfileUpdate(**sync_data))

        except Exception as e:
            logger.error(f"Failed to sync Twitter data for user {user_id}: {str(e)}")
            raise Exception("同步Twitter数据失败")

    def unbind_twitter_account(self, user_id: int) -> bool:
        """解除Twitter绑定"""
        try:
            profile = self.get_kol_profile_by_user_id(user_id)
            if not profile:
                return False

            # 🔧 修复：清除Twitter相关数据，考虑数据库 NOT NULL 约束
            clear_data = {
                'platform': None,
                'platform_name': None,
                'profile_url': None,
                'profile_image_url': None,
                'verified': False,
                'verified_type': None,
                'followers_count': 0,
                'following_count': 0,
                'tweet_count': 0,
                'listed_count': 0,
                'like_count': 0,
                'description': None,
                'location': None,
                'account_created_at': None,
                'last_synced_at': None
                # 注意：不更新 tag_name，因为它是 NOT NULL 且应该保留
            }

            self.update_kol_profile(profile.id, KolProfileUpdate(**clear_data))
            return True

        except Exception as e:
            logger.error(f"Failed to unbind Twitter account for user {user_id}: {str(e)}")
            return False

    def get_twitter_binding_status(self, user_id: int) -> dict:
        """获取Twitter绑定状态"""
        try:
            profile = self.get_kol_profile_by_user_id(user_id)

            if not profile or not profile.platform_username:
                return {"bound": False, "data": None}

            return {
                "bound": True,
                "data": {
                    "platform_username": profile.platform_username,
                    "platform_name": profile.platform_name,
                    "profile_image_url": profile.profile_image_url,
                    "followers_count": profile.followers_count,
                    "following_count": profile.following_count,
                    "tweet_count": profile.tweet_count,
                    "verified": profile.verified,
                    "verified_type": profile.verified_type,
                    "last_synced_at": profile.last_synced_at
                }
            }

        except Exception as e:
            logger.error(f"Failed to get Twitter binding status for user {user_id}: {str(e)}")
            return {"bound": False, "data": None}

    def _format_twitter_data(self, twitter_data: dict) -> dict:
        """格式化Twitter数据为KOL Profile格式"""
        return {
            'platform': 'twitter',
            'platform_id': twitter_data['platform_id'],
            'platform_username': twitter_data['platform_username'],
            'platform_name': twitter_data['platform_name'],
            'profile_url': twitter_data['profile_url'],
            'profile_image_url': twitter_data['profile_image_url'],
            'verified': twitter_data['verified'],
            'verified_type': twitter_data.get('verified_type'),
            'followers_count': twitter_data['followers_count'],
            'following_count': twitter_data['following_count'],
            'tweet_count': twitter_data['tweet_count'],
            'listed_count': twitter_data.get('listed_count', 0),
            'like_count': twitter_data.get('like_count', 0),
            'description': twitter_data.get('description'),
            'location': twitter_data.get('location'),
            'account_created_at': twitter_data.get('account_created_at'),
            'last_synced_at': datetime.now()
        }

# 移除这个引用，因为已经在文件开头导入了
# kol_profile = Table('kol_profile', metadata, autoload_with=engine)


# 保留兼容性的函数（逐步废弃）
def get_kol_profile():
    """废弃：请使用 KolProfileService"""
    logger.warning("get_kol_profile function is deprecated, use KolProfileService instead")
    from models.db import get_db
    with get_db() as db:
        service = KolProfileService(db)
        return service.get_kol_profiles() 